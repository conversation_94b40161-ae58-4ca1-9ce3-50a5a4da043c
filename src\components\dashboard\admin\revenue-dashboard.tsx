"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  useRevenueComparison,
  useRevenueStats,
} from "@/hooks/use-revenue-stats";
import { formatCurrency } from "@/lib/utils/format";
import { motion } from "framer-motion";
import {
  BarChart3,
  Calendar,
  DollarSign,
  MapPin,
  PieChart,
  RefreshCw,
  TrendingDown,
  TrendingUp,
} from "lucide-react";
import { useState } from "react";

interface RevenueCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
  trend?: number;
  format?: "currency" | "number" | "percentage";
}

function RevenueCard({
  title,
  value,
  icon,
  color,
  trend,
  format = "currency",
}: RevenueCardProps) {
  const formatValue = (val: number) => {
    switch (format) {
      case "currency":
        return formatCurrency(val);
      case "percentage":
        return `${val.toFixed(1)}%`;
      default:
        return val.toLocaleString();
    }
  };

  return (
    <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <p className="text-sm font-medium text-neutral-600">{title}</p>
            <p className="text-2xl font-bold text-neutral-900">
              {formatValue(value)}
            </p>
            {trend !== undefined && (
              <div className="flex items-center gap-1">
                {trend >= 0 ? (
                  <TrendingUp className="w-4 h-4 text-green-600" />
                ) : (
                  <TrendingDown className="w-4 h-4 text-red-600" />
                )}
                <span
                  className={`text-sm font-medium ${
                    trend >= 0 ? "text-green-600" : "text-red-600"
                  }`}
                >
                  {Math.abs(trend).toFixed(1)}%
                </span>
              </div>
            )}
          </div>
          <div
            className={`w-12 h-12 rounded-lg ${color} flex items-center justify-center`}
          >
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function RevenueDashboard() {
  const [selectedPeriod, setSelectedPeriod] = useState("30days");

  // Calculer les dates selon la période sélectionnée
  const getPeriodDates = (period: string) => {
    const now = new Date();
    let startDate: Date;

    switch (period) {
      case "7days":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "30days":
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case "90days":
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case "thisMonth":
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case "lastMonth":
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const endDate = new Date(now.getFullYear(), now.getMonth(), 0);
        return {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        };
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    return { startDate: startDate.toISOString(), endDate: now.toISOString() };
  };

  const { startDate, endDate } = getPeriodDates(selectedPeriod);
  const { stats, isLoading, error, refresh } = useRevenueStats({
    startDate,
    endDate,
  });

  // Statistiques de comparaison avec la période précédente
  const previousPeriodDates = (() => {
    const current = new Date(endDate);
    const start = new Date(startDate);
    const duration = current.getTime() - start.getTime();
    const previousEnd = new Date(start.getTime() - 1);
    const previousStart = new Date(previousEnd.getTime() - duration);

    return {
      startDate: previousStart.toISOString(),
      endDate: previousEnd.toISOString(),
    };
  })();

  const { comparison } = useRevenueComparison(
    { startDate, endDate },
    previousPeriodDates
  );

  const handleRefresh = () => {
    refresh();
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-accent-primary" />
            <p className="text-neutral-600">
              Chargement des statistiques de revenus...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !stats) {
    return (
      <Card className="border-0 shadow-lg">
        <CardContent className="text-center py-12">
          <DollarSign className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-neutral-900 mb-2">
            Erreur de chargement
          </h3>
          <p className="text-neutral-600 mb-4">
            {error || "Impossible de charger les statistiques de revenus"}
          </p>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Réessayer
          </Button>
        </CardContent>
      </Card>
    );
  }

  const distributionData = [
    {
      name: "Plateforme",
      value: stats.platformShare,
      color: "bg-blue-500",
      percentage: 15,
    },
    {
      name: "Communes",
      value: stats.communeShare,
      color: "bg-green-500",
      percentage: 10,
    },
    {
      name: "Quartiers",
      value: stats.quartierShare,
      color: "bg-yellow-500",
      percentage: 25,
    },
    {
      name: "Partenaire",
      value: stats.partnerShare,
      color: "bg-purple-500",
      percentage: 10,
    },
    {
      name: "Salaires",
      value: stats.payrollShare,
      color: "bg-red-500",
      percentage: 40,
    },
  ];

  return (
    <div className="space-y-6">
      {/* En-tête avec contrôles */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h2 className="text-2xl font-bold text-neutral-900">
            Tableau de bord des revenus
          </h2>
          <p className="text-neutral-600">
            Suivi des revenus et de leur distribution
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">7 derniers jours</SelectItem>
              <SelectItem value="30days">30 derniers jours</SelectItem>
              <SelectItem value="90days">90 derniers jours</SelectItem>
              <SelectItem value="thisMonth">Ce mois</SelectItem>
              <SelectItem value="lastMonth">Mois dernier</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4" />
          </Button>
        </div>
      </motion.div>

      {/* Cartes de statistiques principales */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        <RevenueCard
          title="Revenus totaux"
          value={stats.totalRevenue}
          icon={<DollarSign className="w-6 h-6 text-white" />}
          color="bg-gradient-to-r from-green-500 to-green-600"
          trend={comparison?.revenueVariation}
        />
        <RevenueCard
          title="Distributions"
          value={stats.totalDistributions}
          icon={<BarChart3 className="w-6 h-6 text-white" />}
          color="bg-gradient-to-r from-blue-500 to-blue-600"
          trend={comparison?.distributionsVariation}
          format="number"
        />
        <RevenueCard
          title="Montant moyen"
          value={stats.averageAmount}
          icon={<TrendingUp className="w-6 h-6 text-white" />}
          color="bg-gradient-to-r from-purple-500 to-purple-600"
          trend={comparison?.averageAmountVariation}
        />
        <RevenueCard
          title="Taux de conversion"
          value={85.2}
          icon={<PieChart className="w-6 h-6 text-white" />}
          color="bg-gradient-to-r from-orange-500 to-orange-600"
          format="percentage"
        />
      </motion.div>

      {/* Distribution des revenus */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="grid grid-cols-1 lg:grid-cols-2 gap-6"
      >
        {/* Répartition des revenus */}
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="w-5 h-5 text-accent-primary" />
              Répartition des revenus
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {distributionData.map((item, index) => (
              <motion.div
                key={item.name}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 + index * 0.1 }}
                className="flex items-center justify-between"
              >
                <div className="flex items-center gap-3">
                  <div className={`w-4 h-4 rounded ${item.color}`} />
                  <span className="font-medium text-neutral-900">
                    {item.name}
                  </span>
                  <Badge variant="outline" className="text-xs">
                    {item.percentage}%
                  </Badge>
                </div>
                <span className="font-semibold text-neutral-900">
                  {formatCurrency(item.value)}
                </span>
              </motion.div>
            ))}
          </CardContent>
        </Card>

        {/* Top quartiers */}
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="w-5 h-5 text-accent-primary" />
              Top quartiers
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {stats.topQuartiers?.slice(0, 5).map((quartier, index) => (
              <motion.div
                key={quartier.quartier}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 + index * 0.1 }}
                className="flex items-center justify-between"
              >
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-accent-primary/10 flex items-center justify-center">
                    <span className="text-sm font-bold text-accent-primary">
                      {index + 1}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium text-neutral-900">
                      {quartier.quartier}
                    </p>
                    <p className="text-xs text-neutral-600">
                      {quartier.count} certificats
                    </p>
                  </div>
                </div>
                <span className="font-semibold text-neutral-900">
                  {formatCurrency(quartier.amount)}
                </span>
              </motion.div>
            )) || (
              <p className="text-center text-neutral-600 py-4">
                Aucune donnée disponible
              </p>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* Évolution temporelle */}
      {stats.distributionsByDay && stats.distributionsByDay.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5 text-accent-primary" />
                Évolution des revenus
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {stats.distributionsByDay.slice(-7).map((day) => (
                  <div
                    key={day.date}
                    className="flex items-center justify-between"
                  >
                    <span className="text-sm text-neutral-600">
                      {new Date(day.date).toLocaleDateString()}
                    </span>
                    <div className="flex items-center gap-3">
                      <span className="text-sm text-neutral-600">
                        {day.count} distributions
                      </span>
                      <span className="font-semibold text-neutral-900">
                        {formatCurrency(day.amount)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
}
